from npiregistry_client import fetch_paginated
import json

# Get 50 dentists in New York (if available), 10 at a time:
count = 0
results = []
for dentist in fetch_paginated(
    taxonomy_description="Dentist", state="NY", batch_size=10, max_records=50
):
    results.append(dentist)
    count += 1

print(f"Retrieved {count} dentist records in NY (up to 50).")

with open("dentists_ny.json", "w") as f:
    json.dump(results, f, indent=2)
    
print(f"Results saved to dentists_ny.json")

    
